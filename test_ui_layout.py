#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI布局优化效果
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QDoubleSpinBox, QCheckBox, QSizePolicy
)
from PyQt5.QtCore import Qt

class TestColorRangeWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        self.setWindowTitle('颜色范围控件测试 - 小窗口适配')
        self.setGeometry(100, 100, 400, 300)  # 设置较小的窗口尺寸
        
        main_layout = QVBoxLayout(self)
        
        # 添加标题
        title = QLabel("🎨 颜色去重功能测试")
        title.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                font-weight: bold;
                color: #667eea;
                padding: 10px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title)
        
        # 创建表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        form_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        form_layout.setLabelAlignment(Qt.AlignLeft)
        
        # 添加启用复选框
        checkbox = QCheckBox("启用颜色去重")
        checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        form_layout.addRow(checkbox)
        
        # 添加颜色范围控件
        self.add_color_range_widgets(form_layout, "色调", -180, 180, 1, -2.0, 10.0)
        self.add_color_range_widgets(form_layout, "亮度", -1.0, 1.0, 0.01, -0.05, 0.05)
        self.add_color_range_widgets(form_layout, "对比度", -4, 4, 0.01, -0.2, 0.2)
        self.add_color_range_widgets(form_layout, "饱和度", -1, 5, 0.01, -0.2, 0.2)
        self.add_color_range_widgets(form_layout, "伽马", -1, 1, 0.01, -0.3, 0.3)
        
        main_layout.addLayout(form_layout)
        main_layout.addStretch()
        
    def add_color_range_widgets(self, layout, name, min_range, max_range, step, min_val, max_val):
        """添加优化后的颜色范围控件"""
        min_spin = QDoubleSpinBox()
        max_spin = QDoubleSpinBox()
        min_spin.setRange(min_range, max_range)
        min_spin.setSingleStep(step)
        max_spin.setRange(min_range, max_range)
        max_spin.setSingleStep(step)
        min_spin.setValue(min_val)
        max_spin.setValue(max_val)

        # 优化样式 - 减少最小宽度，提高小窗口适应性
        spinbox_style = """
            QDoubleSpinBox {
                padding: 4px 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
                min-width: 55px;
                max-width: 80px;
                font-size: 10pt;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
            }
            QDoubleSpinBox:hover {
                border-color: #94a3b8;
            }
        """
        min_spin.setStyleSheet(spinbox_style)
        max_spin.setStyleSheet(spinbox_style)

        # 创建紧凑的水平布局
        range_layout = QHBoxLayout()
        range_layout.setSpacing(5)  # 减少间距
        
        # 使用更短的标签文字
        min_label = QLabel("最小:")
        min_label.setStyleSheet("""
            font-weight: bold; 
            color: #64748b; 
            font-size: 9pt;
            min-width: 28px;
            max-width: 28px;
        """)
        
        max_label = QLabel("最大:")
        max_label.setStyleSheet("""
            font-weight: bold; 
            color: #64748b; 
            font-size: 9pt;
            min-width: 28px;
            max-width: 28px;
        """)

        # 紧凑排列控件
        range_layout.addWidget(min_label)
        range_layout.addWidget(min_spin)
        range_layout.addWidget(max_label)
        range_layout.addWidget(max_spin)
        range_layout.addStretch(1)  # 添加弹性空间

        range_widget = QWidget()
        range_widget.setLayout(range_layout)
        range_widget.setContentsMargins(0, 0, 0, 0)
        
        # 设置控件大小策略，确保能够适应窗口大小
        range_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        range_widget.setMinimumWidth(200)  # 设置最小宽度
        
        layout.addRow(f"{name} 范围:", range_widget)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyleSheet("""
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background-color: #f8fafc;
        }
    """)
    
    window = TestColorRangeWidget()
    window.show()
    
    print("测试窗口已启动")
    print("请调整窗口大小来测试布局适应性")
    print("- 在小窗口下，控件应该保持在同一行")
    print("- 标签文字已优化为更短的版本")
    print("- 控件宽度已减小以适应小窗口")
    
    sys.exit(app.exec_())
