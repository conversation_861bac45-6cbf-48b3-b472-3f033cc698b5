#!/usr/bin/env python3
import ast
import sys

def check_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析语法
        ast.parse(source)
        print(f"✅ {filename} 语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filename} 检查失败: {e}")
        return False

if __name__ == "__main__":
    filename = "main_module.py"
    if check_syntax(filename):
        print("语法检查通过，可以尝试运行")
    else:
        print("发现语法错误，需要修复")
